program DelphiSecrets.UIAutomation;

{$APPTYPE CONSOLE}

{$R *.res}

uses
  Winapi.Windows,
  Winapi.Messages,
  System.SysUtils,
  System.Variants,
  ShellAPI,
  ActiveX,
  UIAutomationClient_TLB in 'UIAutomationClient_TLB.pas';

const
  UIA_NamePropertyId = 30005;
  UIA_AutomationIdPropertyId = 30011;
  UIA_ControlTypePropertyId = 30003;
  UIA_ValueValuePropertyId = 30045;

  UIA_EditControlTypeId = 50004;
  UIA_ButtonControlTypeId = 50000;

  UIA_InvokePatternId = 10000;
  UIA_ValuePatternId = 10002;

  TreeScope_Element = 1;
  TreeScope_Children = 2;
  TreeScope_Descendants = 4;
  TreeScope_Parent = 8;
  TreeScope_Ancestors = 16;
  TreeScope_Subtree = TreeScope_Element or TreeScope_Children or TreeScope_Descendants;

procedure SendText(const AText: string);
var
  i: Integer;
  LInput: TInput;
begin
  for i := 1 to Length(AText) do
  begin
    FillChar(LInput, SizeOf(LInput), 0);
    LInput.Itype := INPUT_KEYBOARD;
    LInput.ki.wVk := 0;
    LInput.ki.wScan := Ord(AText[i]);
    LInput.ki.dwFlags := KEYEVENTF_UNICODE;
    SendInput(1, @LInput, SizeOf(LInput));

    LInput.ki.dwFlags := KEYEVENTF_UNICODE or KEYEVENTF_KEYUP;
    SendInput(1, @LInput, SizeOf(LInput));
  end;
end;

procedure AutomateNotepad;
var
  LUIA: IUIAutomation;
  LRootEl, LNotepadEl, LEditEl, LSaveDialog, LFilenameBox, LSaveButton: IUIAutomationElement;
  LCond, LCondSaveAs1, LCondSaveAs2, LCondSaveAs: IUIAutomationCondition;
  LInvoke: IUIAutomationInvokePattern;
  LValue: IUIAutomationValuePattern;
  LNotepadHandle: HWND;
  LWndPtr: Pointer;
  LShellExecInfo: TShellExecuteInfo;
  LTimestamp, LFilename: string;
begin
  CoInitialize(nil);
  try
    // Launch Notepad
    FillChar(LShellExecInfo, SizeOf(LShellExecInfo), 0);
    LShellExecInfo.cbSize := SizeOf(LShellExecInfo);
    LShellExecInfo.fMask := SEE_MASK_NOCLOSEPROCESS;
    LShellExecInfo.lpFile := 'notepad.exe';
    LShellExecInfo.nShow := SW_SHOW;
    ShellExecuteEx(@LShellExecInfo);

    // Wait for the window to appear
    repeat
      Sleep(100);
      LNotepadHandle := FindWindow('Notepad', nil);
    until LNotepadHandle <> 0;

    Sleep(1000); // Allow UI to initialize

    // Initialize UI Automation
    LUIA := CoCUIAutomation.Create;
    LUIA.GetRootElement(LRootEl);

    // Locate Notepad window
    LUIA.CreatePropertyCondition(UIA_NamePropertyId, 'Unbenannt - Editor', LCond);
    LRootEl.FindFirst(TreeScope_Children, LCond, LNotepadEl);

    // Find edit field and insert text
    LUIA.CreatePropertyCondition(UIA_ControlTypePropertyId, UIA_EditControlTypeId, LCond);
    LNotepadEl.FindFirst(TreeScope_Subtree, LCond, LEditEl);

    if Assigned(LEditEl) and
      Succeeded(LEditEl.GetCurrentPattern(UIA_ValuePatternId, IInterface(LValue))) then
    begin
      LValue.SetValue('Hello World');
    end;

    // Trigger Save As via CTRL + SHIFT + S
    keybd_event(VK_CONTROL, 0, 0, 0);
    keybd_event(VK_SHIFT, 0, 0, 0);
    keybd_event(Ord('S'), 0, 0, 0);
    keybd_event(Ord('S'), 0, KEYEVENTF_KEYUP, 0);
    keybd_event(VK_SHIFT, 0, KEYEVENTF_KEYUP, 0);
    keybd_event(VK_CONTROL, 0, KEYEVENTF_KEYUP, 0);
    Sleep(1000);

    // Locate Save As dialog
    LUIA.CreatePropertyCondition(UIA_NamePropertyId, 'Speichern unter', LCondSaveAs1);
    LUIA.CreatePropertyCondition(UIA_NamePropertyId, 'Save As', LCondSaveAs2);
    LUIA.CreateOrCondition(LCondSaveAs1, LCondSaveAs2, LCondSaveAs);
    LRootEl.FindFirst(TreeScope_Subtree, LCondSaveAs, LSaveDialog);

    if not Assigned(LSaveDialog) then
      raise Exception.Create('Save As dialog not found.');

    // Bring Save As dialog to foreground
    if LSaveDialog.Get_CurrentNativeWindowHandle(LWndPtr) = S_OK then
    begin
      SetForegroundWindow(HWND(LWndPtr));
      Sleep(300);
    end;

    // Generate timestamp-based filename
    LTimestamp := FormatDateTime('yyyymmdd_hhnnss', Now);
    LFilename := 'C:\temp\helloworld_' + LTimestamp + '.txt';

    // Type file name
    SendText(LFilename);
    Sleep(300);

    // Click Save button
    LUIA.CreatePropertyCondition(UIA_NamePropertyId, 'Speichern', LCond);
    LSaveDialog.FindFirst(TreeScope_Descendants, LCond, LSaveButton);

    if Assigned(LSaveButton) and
      Succeeded(LSaveButton.GetCurrentPattern(UIA_InvokePatternId, IInterface(LInvoke))) then
    begin
      LInvoke.Invoke;
    end
    else
    begin
      // Fallback: Press ENTER if Invoke fails
      keybd_event(VK_RETURN, 0, 0, 0);
      keybd_event(VK_RETURN, 0, KEYEVENTF_KEYUP, 0);
    end;

    // Close Notepad
    PostMessage(LNotepadHandle, WM_CLOSE, 0, 0);

  finally
    CoUninitialize;
  end;
end;

begin
  try
    AutomateNotepad;
  except
    on E: Exception do
      Writeln(E.ClassName, ': ', E.Message);
  end;
end.

