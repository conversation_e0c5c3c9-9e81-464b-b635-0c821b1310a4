﻿program DelphiSecrets.UIAutomation;

{$APPTYPE CONSOLE}

{$R *.res}

uses
  Winapi.Windows,
  Winapi.Messages,
  System.SysUtils,
  System.Variants,
  ShellAPI,
  ActiveX,
  UIAutomationClient_TLB in '..\..\..\Documents\Embarcadero\Studio\23.0\Imports\UIAutomationClient_TLB.pas';

const
  UIA_NamePropertyId = 30005;
  UIA_AutomationIdPropertyId = 30011;
  UIA_ControlTypePropertyId = 30003;
  UIA_ValueValuePropertyId = 30045;

  UIA_EditControlTypeId = 50004;
  UIA_ButtonControlTypeId = 50000;

  UIA_InvokePatternId = 10000;
  UIA_ValuePatternId = 10002;

  TreeScope_Element = 1;
  TreeScope_Children = 2;
  TreeScope_Descendants = 4;
  TreeScope_Parent = 8;
  TreeScope_Ancestors = 16;
  TreeScope_Subtree = TreeScope_Element or TreeScope_Children or TreeScope_Descendants;

procedure AutomateNotepad;
var
  UIA: IUIAutomation;
  RootEl, NotepadEl, EditEl, SaveDialog, FilenameBox, SaveButton: IUIAutomationElement;
  Cond, CondSaveAs1, CondSaveAs2, CondSaveAs: IUIAutomationCondition;
  Invoke: IUIAutomationInvokePattern;
  Value: IUIAutomationValuePattern;
  hNotepad: HWND;
  SI: TShellExecuteInfo;
begin
  CoInitialize(nil);
  try
    // Starte Notepad
    FillChar(SI, SizeOf(SI), 0);
    SI.cbSize := SizeOf(SI);
    SI.fMask := SEE_MASK_NOCLOSEPROCESS;
    SI.lpFile := 'notepad.exe';
    SI.nShow := SW_SHOW;
    ShellExecuteEx(@SI);

    // Warten bis Fenster erscheint
    repeat
      Sleep(100);
      hNotepad := FindWindow('Notepad', nil);
    until hNotepad <> 0;

    Sleep(1000); // UI bereit werden lassen

    // UI Automation initialisieren
    UIA := CoCUIAutomation.Create;
    UIA.GetRootElement(RootEl);

    // Notepad-Fenster finden
    UIA.CreatePropertyCondition(UIA_NamePropertyId, 'Unbenannt - Editor', Cond);
    RootEl.FindFirst(TreeScope_Children, Cond, NotepadEl);

    // Edit-Feld finden und "Hello World" schreiben
    UIA.CreatePropertyCondition(UIA_ControlTypePropertyId, UIA_EditControlTypeId, Cond);
    NotepadEl.FindFirst(TreeScope_Subtree, Cond, EditEl);

    if Assigned(EditEl) and
      Succeeded(EditEl.GetCurrentPattern(UIA_ValuePatternId, IInterface(Value))) then
    begin
      Value.SetValue('Hello World');
    end;

    // "Speichern unter..." mit STRG + SHIFT + S
    keybd_event(VK_CONTROL, 0, 0, 0);
    keybd_event(VK_SHIFT, 0, 0, 0);
    keybd_event(Ord('S'), 0, 0, 0);
    keybd_event(Ord('S'), 0, KEYEVENTF_KEYUP, 0);
    keybd_event(VK_SHIFT, 0, KEYEVENTF_KEYUP, 0);
    keybd_event(VK_CONTROL, 0, KEYEVENTF_KEYUP, 0);
    Sleep(1000);

    // Speichern unter-Dialog finden
    UIA.CreatePropertyCondition(UIA_NamePropertyId, 'Speichern unter', CondSaveAs1);
    UIA.CreatePropertyCondition(UIA_NamePropertyId, 'Save As', CondSaveAs2);
    UIA.CreateOrCondition(CondSaveAs1, CondSaveAs2, CondSaveAs);
    RootEl.FindFirst(TreeScope_Subtree, CondSaveAs, SaveDialog);

    if not Assigned(SaveDialog) then
      raise Exception.Create('Speichern unter-Dialog nicht gefunden.');

    // Dateiname eingeben
    UIA.CreatePropertyCondition(UIA_ControlTypePropertyId, UIA_EditControlTypeId, Cond);
    SaveDialog.FindFirst(TreeScope_Descendants, Cond, FilenameBox);

    if Assigned(FilenameBox) and
      Succeeded(FilenameBox.GetCurrentPattern(UIA_ValuePatternId, IInterface(Value))) then
    begin
      Value.SetValue('C:\temp\helloworld.txt');
    end;

    Sleep(300);

    // Speichern-Button klicken
    UIA.CreatePropertyCondition(UIA_NamePropertyId, 'Speichern', Cond);
    SaveDialog.FindFirst(TreeScope_Descendants, Cond, SaveButton);

    if Assigned(SaveButton) and
      Succeeded(SaveButton.GetCurrentPattern(UIA_InvokePatternId, IInterface(Invoke))) then
    begin
      Invoke.Invoke;
    end;

  finally
    CoUninitialize;
  end;
end;

begin
  try
    AutomateNotepad;
  except
    on E: Exception do
      Writeln(E.ClassName, ': ', E.Message);
  end;
end.

