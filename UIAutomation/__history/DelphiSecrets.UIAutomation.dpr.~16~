﻿program DelphiSecrets.UIAutomation;

{$APPTYPE CONSOLE}

{$R *.res}

uses
  Winapi.Windows,
  Winapi.Messages,
  System.SysUtils,
  System.Variants,
  ShellAPI,
  ActiveX,
  UIAutomationClient_TLB in '..\..\..\Documents\Embarcadero\Studio\23.0\Imports\UIAutomationClient_TLB.pas';

const
  UIA_NamePropertyId = 30005;
  UIA_AutomationIdPropertyId = 30011;
  UIA_ControlTypePropertyId = 30003;
  UIA_ValueValuePropertyId = 30045;

  UIA_EditControlTypeId = 50004;
  UIA_ButtonControlTypeId = 50000;

  UIA_InvokePatternId = 10000;
  UIA_ValuePatternId = 10002;

  TreeScope_Element = 1;
  TreeScope_Children = 2;
  TreeScope_Descendants = 4;
  TreeScope_Parent = 8;
  TreeScope_Ancestors = 16;
  TreeScope_Subtree = TreeScope_Element or TreeScope_Children or TreeScope_Descendants;

procedure SendText(const Text: string);
var
  I: Integer;
  Input: TInput;
begin
  for I := 1 to Length(Text) do
  begin
    FillChar(Input, SizeOf(Input), 0);
    Input.Itype := INPUT_KEYBOARD;
    Input.ki.wVk := 0;
    Input.ki.wScan := Ord(Text[I]);
    Input.ki.dwFlags := KEYEVENTF_UNICODE;
    SendInput(1, @Input, SizeOf(Input));

    Input.ki.dwFlags := KEYEVENTF_UNICODE or KEYEVENTF_KEYUP;
    SendInput(1, @Input, SizeOf(Input));
  end;
end;

procedure AutomateNotepad;
var
  UIA: IUIAutomation;
  RootEl, NotepadEl, EditEl, SaveDialog, FilenameBox, SaveButton: IUIAutomationElement;
  Cond, CondSaveAs1, CondSaveAs2, CondSaveAs: IUIAutomationCondition;
  Invoke: IUIAutomationInvokePattern;
  Value: IUIAutomationValuePattern;
  hNotepad: HWND;
  wndPtr: Pointer;
  SI: TShellExecuteInfo;
begin
  CoInitialize(nil);
  try
    // Starte Notepad
    FillChar(SI, SizeOf(SI), 0);
    SI.cbSize := SizeOf(SI);
    SI.fMask := SEE_MASK_NOCLOSEPROCESS;
    SI.lpFile := 'notepad.exe';
    SI.nShow := SW_SHOW;
    ShellExecuteEx(@SI);

    // Warten bis Fenster erscheint
    repeat
      Sleep(100);
      hNotepad := FindWindow('Notepad', nil);
    until hNotepad <> 0;

    Sleep(1000); // UI bereit werden lassen

    // UI Automation initialisieren
    UIA := CoCUIAutomation.Create;
    UIA.GetRootElement(RootEl);

    // Notepad-Fenster finden
    UIA.CreatePropertyCondition(UIA_NamePropertyId, 'Unbenannt - Editor', Cond);
    RootEl.FindFirst(TreeScope_Children, Cond, NotepadEl);

    // Edit-Feld finden und "Hello World" schreiben
    UIA.CreatePropertyCondition(UIA_ControlTypePropertyId, UIA_EditControlTypeId, Cond);
    NotepadEl.FindFirst(TreeScope_Subtree, Cond, EditEl);

    if Assigned(EditEl) and
      Succeeded(EditEl.GetCurrentPattern(UIA_ValuePatternId, IInterface(Value))) then
    begin
      Value.SetValue('Hello World');
    end;

    // "Speichern unter..." mit STRG + SHIFT + S
    keybd_event(VK_CONTROL, 0, 0, 0);
    keybd_event(VK_SHIFT, 0, 0, 0);
    keybd_event(Ord('S'), 0, 0, 0);
    keybd_event(Ord('S'), 0, KEYEVENTF_KEYUP, 0);
    keybd_event(VK_SHIFT, 0, KEYEVENTF_KEYUP, 0);
    keybd_event(VK_CONTROL, 0, KEYEVENTF_KEYUP, 0);
    Sleep(1000);

    // Speichern unter-Dialog finden
    UIA.CreatePropertyCondition(UIA_NamePropertyId, 'Speichern unter', CondSaveAs1);
    UIA.CreatePropertyCondition(UIA_NamePropertyId, 'Save As', CondSaveAs2);
    UIA.CreateOrCondition(CondSaveAs1, CondSaveAs2, CondSaveAs);
    RootEl.FindFirst(TreeScope_Subtree, CondSaveAs, SaveDialog);

    if not Assigned(SaveDialog) then
      raise Exception.Create('Speichern unter-Dialog nicht gefunden.');

    // Fenster in den Vordergrund holen
    if SaveDialog.Get_CurrentNativeWindowHandle(wndPtr) = S_OK then
    begin
      SetForegroundWindow(HWND(wndPtr));
      Sleep(300);
    end;

    // Dateiname eintippen
    SendText('C:\temp\helloworld.txt');
    Sleep(300);

    // Speichern-Button klicken
    UIA.CreatePropertyCondition(UIA_NamePropertyId, 'Speichern', Cond);
    SaveDialog.FindFirst(TreeScope_Descendants, Cond, SaveButton);

    if Assigned(SaveButton) and
      Succeeded(SaveButton.GetCurrentPattern(UIA_InvokePatternId, IInterface(Invoke))) then
    begin
      Invoke.Invoke;
    end
    else
    begin
      // Fallback: ENTER drücken, falls Invoke nicht funktioniert
      keybd_event(VK_RETURN, 0, 0, 0);
      keybd_event(VK_RETURN, 0, KEYEVENTF_KEYUP, 0);
    end;

    // Notepad beenden
    PostMessage(hNotepad, WM_CLOSE, 0, 0);

  finally
    CoUninitialize;
  end;
end;

begin
  try
    AutomateNotepad;
  except
    on E: Exception do
      Writeln(E.ClassName, ': ', E.Message);
  end;
end.
